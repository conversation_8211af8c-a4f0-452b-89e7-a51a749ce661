<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Message Recorder</title>
    <script src="https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="config.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            overflow: hidden;
            margin: 0;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .app-container {
            position: relative;
            z-index: 2;
            width: 334px;
            height: 364px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 16px;
            box-shadow:
                0 16px 32px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideIn 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .app-header {
            text-align: center;
            margin-bottom: 12px;
            flex-shrink: 0;
        }

        .app-title {
            font-size: 18px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2px;
            letter-spacing: -0.5px;
        }

        .app-subtitle {
            color: #64748b;
            font-size: 12px;
            font-weight: 400;
        }

        .recording-area {
            text-align: center;
            margin: 8px 0;
            position: relative;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 140px;
        }

        .record-button-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding-top: 10px;
        }

        .record-button {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            box-shadow:
                0 12px 24px rgba(255, 107, 107, 0.3),
                0 0 0 0 rgba(255, 107, 107, 0.4);
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .record-button::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24, #ff6b6b);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .record-button:hover {
            transform: scale(1.05);
            box-shadow:
                0 25px 50px rgba(255, 107, 107, 0.4),
                0 0 0 8px rgba(255, 107, 107, 0.1);
        }

        .record-button:hover::before {
            opacity: 1;
        }

        .record-button.recording {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            animation: recordingPulse 2s infinite;
            box-shadow:
                0 20px 40px rgba(255, 71, 87, 0.4),
                0 0 0 0 rgba(255, 71, 87, 0.6);
        }

        @keyframes recordingPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow:
                    0 20px 40px rgba(255, 71, 87, 0.4),
                    0 0 0 0 rgba(255, 71, 87, 0.6);
            }
            50% {
                transform: scale(1.08);
                box-shadow:
                    0 25px 50px rgba(255, 71, 87, 0.5),
                    0 0 0 15px rgba(255, 71, 87, 0.2);
            }
        }

        .record-icon {
            font-size: 28px;
            transition: all 0.3s ease;
        }

        .record-button.recording .record-icon {
            animation: iconBounce 1s infinite;
        }

        @keyframes iconBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .status-message {
            font-size: 14px;
            font-weight: 500;
            color: #475569;
            min-height: 18px;
            transition: all 0.3s ease;
        }

        .status-message.recording {
            display: none;
        }

        .recording-timer {
            font-size: 11px;
            color: #64748b;
            font-weight: 400;
            min-height: 14px;
        }

        .preview-section {
            margin: 8px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            flex-shrink: 0;
        }

        .preview-section.show {
            opacity: 1;
            transform: translateY(0);
        }

        .audio-preview {
            width: 100%;
            height: 35px;
            border-radius: 10px;
            outline: none;
            margin-bottom: 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .action-btn {
            width: 42px;
            height: 42px;
            border-radius: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-btn:hover::before {
            opacity: 0.1;
        }

        .btn-send {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
        }

        .btn-send:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 12px 24px rgba(16, 185, 129, 0.4);
        }

        .btn-discard {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            box-shadow: 0 8px 16px rgba(239, 68, 68, 0.3);
        }

        .btn-discard:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 12px 24px rgba(239, 68, 68, 0.4);
        }

        .processing-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 12px;
            text-align: center;
            margin: 8px 0;
            font-weight: 600;
            font-size: 12px;
            box-shadow: 0 6px 12px rgba(245, 158, 11, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            flex-shrink: 0;
        }

        .processing-warning.show {
            opacity: 1;
            transform: translateY(0);
        }

        .tooltip {
            position: absolute;
            bottom: -32px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid rgba(0, 0, 0, 0.8);
        }

        .action-btn:hover .tooltip {
            opacity: 1;
            transform: translateX(-50%) translateY(-4px);
        }

        .wave-animation {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 24px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .recording-area.recording .wave-animation {
            opacity: 1;
        }

        .wave {
            width: 2px;
            height: 12px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            margin: 0 1px;
            border-radius: 1px;
            display: inline-block;
            animation: wave 1.2s infinite ease-in-out;
        }

        .wave:nth-child(2) { animation-delay: -1.1s; }
        .wave:nth-child(3) { animation-delay: -1.0s; }
        .wave:nth-child(4) { animation-delay: -0.9s; }
        .wave:nth-child(5) { animation-delay: -0.8s; }
        .wave:nth-child(6) { animation-delay: -0.7s; }
        .wave:nth-child(7) { animation-delay: -0.6s; }
        .wave:nth-child(8) { animation-delay: -0.5s; }

        @keyframes wave {
            0%, 40%, 100% {
                transform: scaleY(0.4);
                opacity: 0.5;
            }
            20% {
                transform: scaleY(1);
                opacity: 1;
            }
        }

        .status-alert {
            border-radius: 10px;
            border: none;
            padding: 8px 12px;
            font-weight: 500;
            font-size: 12px;
            margin: 6px 0;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .status-alert.show {
            opacity: 1;
            transform: translateY(0);
        }

        .status-alert.alert-success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.2);
        }

        .status-alert.alert-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 8px 16px rgba(239, 68, 68, 0.2);
        }

        @media (max-width: 480px) {
            .app-container {
                width: 320px;
                height: 350px;
                padding: 12px;
            }

            .record-button {
                width: 70px;
                height: 70px;
            }

            .record-icon {
                font-size: 24px;
            }

            .action-btn {
                width: 38px;
                height: 38px;
                font-size: 14px;
            }

            .app-title {
                font-size: 16px;
            }

            .app-subtitle {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1 class="app-title">🎤 Voice Message</h1>
            <p class="app-subtitle">Record and send voice messages instantly</p>
        </div>

        <div class="recording-area" id="recordingArea">
            <div class="record-button-container">
                <button id="recordButton" class="record-button">
                    <i class="fas fa-microphone record-icon"></i>
                </button>

                <div class="wave-animation">
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                </div>
            </div>

            <div class="status-info">
                <div class="status-message" id="statusMessage">Tap to start recording</div>
                <div class="recording-timer" id="recordingTimer"></div>
            </div>
        </div>

        <div class="preview-section" id="previewSection">
            <audio id="preview" class="audio-preview" controls></audio>

            <div class="action-buttons" id="previewControls">
                <button id="sendButton" class="action-btn btn-send">
                    <i class="fas fa-paper-plane"></i>
                    <div class="tooltip">Send Message</div>
                </button>
                <button id="discardButton" class="action-btn btn-discard">
                    <i class="fas fa-trash-alt"></i>
                    <div class="tooltip">Delete Recording</div>
                </button>
            </div>
        </div>

        <div id="processingWarning" class="processing-warning">
            <i class="fas fa-spinner fa-spin"></i> Sending voice message...
            <br><small>Please keep this window open</small>
        </div>

        <div id="statusAlert" class="status-alert"></div>
    </div>

    <script>
        console.log('[index.html] Script loaded at', new Date().toISOString());
        let mediaRecorder;
        let audioChunks = [];
        let recordingTimer;
        let recordingDuration = 0;
        let stream;

        let socket = null;

        // Pre-fetched data during recording
        let preFetchedData = {
            accessToken: null,
            chatMembers: null,
            botAdded: false,
            folderReady: false,
            userContext: null
        };

        async function initializeSocket() {
            if (socket?.connected) {
                return socket;
            }

            return new Promise((resolve, reject) => {
                socket = io(window.APP_CONFIG.SOCKET_URL, {
                    path: '/socket.io',
                    transports: ['polling'],
                    reconnection: true,
                    reconnectionAttempts: 5,
                    reconnectionDelay: 1000,
                    timeout: 60000,
                    query: { client: 'teams', timestamp: Date.now() }
                });

                socket.on('connect', () => {
                    console.log('[index.html] Socket.IO connected successfully');
                    
                    // Initialize context right after connection
                    microsoftTeams.getContext((context) => {
                        console.log('[index.html] Initializing context after socket connection');
                        if (!context.userObjectId && !context.userId) {
                            console.error('[index.html] No user ID in context');
                            showStatusMessage('Failed to retrieve user information.', false);
                            reject(new Error('No user ID in context'));
                            return;
                        }
                        socket.emit('initialize-context', {
                            userObjectId: context.userObjectId || context.userId,
                            userDisplayName: context.userPrincipalName || 'Unknown User',
                            chatId: context.chatId || context.channelId || null
                        });
                        resolve(socket);
                    });
                });

                socket.on('connect_error', (error) => {
                    console.error('[index.html] Socket.IO connection error:', error);
                    showStatusMessage(`Cannot connect to server: ${error.message}. Please try again.`, false);
                    reject(error);
                });

                socket.on('disconnect', () => {
                    console.log('[index.html] Socket.IO disconnected');
                    socket = null;
                });

                socket.on('upload-success', (data) => {
                    console.log('[index.html] Upload success received:', data);
                    
                    // Show success message
                    logAndShow('Voice message sent successfully!', true);

                    // Create result data
                    const result = {
                        messageId: data.messageId,
                        downloadUrl: data.downloadUrl,
                        fileName: data.fileName,
                        chatId: data.chatId,
                        userId: data.userId,
                        userName: data.userName,
                        status: 'complete'
                    };

                    // Clean up resources and close app
                    if (stream) {
                        stream.getTracks().forEach(track => track.stop());
                        stream = null;
                    }
                    audioChunks = [];

                    console.log('[index.html] Cleaning up and closing...');

                    // Disconnect socket
                    if (socket?.connected) {
                        socket.disconnect();
                        socket = null;
                    }

                    // Close Teams dialog after a brief delay to ensure message is shown
                    setTimeout(() => {
                        microsoftTeams.tasks.submitTask(result);
                        window.close();
                    }, 1000);
                });

                // Set connection timeout
                setTimeout(() => {
                    if (!socket.connected) {
                        reject(new Error('Connection timeout'));
                    }
                }, 10000);
            });
        }

        function logAndShow(message, isError = false) {
            console.log(`[index.html] ${message}`);
            showStatusMessage(message, !isError);
        }

        function showStatusMessage(message, isSuccess) {
            const statusAlert = document.getElementById('statusAlert');
            const statusMessage = document.getElementById('statusMessage');

            // Update main status message
            statusMessage.textContent = message;

            // Show alert if it's important
            if (message.includes('❌') || message.includes('✅') || message.includes('⚠️')) {
                statusAlert.innerHTML = message;
                statusAlert.classList.remove('alert-success', 'alert-danger');
                statusAlert.classList.add(isSuccess ? 'alert-success' : 'alert-danger', 'show');

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    statusAlert.classList.remove('show');
                }, 5000);
            }
        }

        // ULTRA-FAST OPTIMIZATION: Authentication cache for instant sending
        let authCache = {
            delegatedToken: null,
            tokenExpiry: null,
            userContext: null,
            isAuthenticated: false
        };

        // OS Detection function
        function detectOS() {
            const userAgent = navigator.userAgent.toLowerCase();
            if (userAgent.includes('linux') || userAgent.includes('ubuntu')) {
                return 'linux';
            } else if (userAgent.includes('win')) {
                return 'windows';
            } else if (userAgent.includes('mac')) {
                return 'mac';
            }
            return 'unknown';
        }

        // Check if running in Teams desktop client (teams-for-linux)
        function isTeamsDesktopClient() {
            const userAgent = navigator.userAgent.toLowerCase();
            // Check for teams-for-linux specifically or general Teams desktop indicators
            return userAgent.includes('teams/') || userAgent.includes('msteams') || userAgent.includes('teams-for-linux') || userAgent.includes('electron');
        }

        // Show Ubuntu teams-for-linux specific message
        function showLinuxMessage() {
            // Replace the status message with Ubuntu warning
            const statusMessage = document.getElementById('statusMessage');
            if (statusMessage) {
                statusMessage.innerHTML = `
                    <div style="
                        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
                        color: #92400e;
                        border: 1px solid #fcd34d;
                        border-radius: 8px;
                        padding: 8px 10px;
                        font-size: 11px;
                        font-weight: 500;
                        line-height: 1.3;
                        box-shadow: 0 2px 4px rgba(251, 191, 36, 0.2);
                        animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
                    ">
                        <div style="font-weight: 600; margin-bottom: 4px; color: #92400e;">
                            ⚠️ Ubuntu Teams-for-Linux Users
                        </div>
                        <div style="margin-bottom: 4px; color: #a16207;">
                            Popups blocked in desktop client
                        </div>
                        <a href="https://teams.microsoft.com" target="_blank"
                           style="color: #1d4ed8; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 4px; justify-content: center;">
                            🌐 Open Teams in Browser
                        </a>
                    </div>
                `;
            }

            // Disable recording functionality
            const recordButton = document.getElementById('recordButton');
            if (recordButton) {
                recordButton.disabled = true;
                recordButton.style.opacity = '0.5';
                recordButton.style.cursor = 'not-allowed';
                recordButton.title = 'Please use Teams in browser on Ubuntu/Linux';
            }
        }

        // Pre-authenticate immediately on app load
        async function preAuthenticate() {
            try {
                console.log('[index.html] 🚀 Starting pre-authentication for ultra-fast sending...');
                const os = detectOS();
                const isDesktopClient = isTeamsDesktopClient();
                if (os === 'linux' && isDesktopClient) {
                    console.log('[index.html] 🐧 Ubuntu teams-for-linux detected - bypassing pre-authentication due to popup blocking');
                    showLinuxMessage();
                    return;
                }
                // Get user context
                const context = await new Promise((resolve) => {
                    microsoftTeams.getContext(resolve);
                });
                authCache.userContext = context;
                console.log('[index.html] ✅ User context cached');
                // Try to use session token if available
                const sessionToken = sessionStorage.getItem('delegatedToken');
                if (sessionToken) {
                    try {
                        const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                            headers: { 'Authorization': `Bearer ${sessionToken}` }
                        });
                        if (response.ok) {
                            authCache.delegatedToken = sessionToken;
                            authCache.isAuthenticated = true;
                            console.log('[index.html] ⚡ Session token is valid (checked with Graph API)');
                            return;
                        }
                    } catch (err) {
                        // Ignore and fallback to auth
                    }
                }
                // If no valid session token, authenticate and store
                return await new Promise((resolve, reject) => {
                    microsoftTeams.authentication.authenticate({
                        url: window.APP_CONFIG.getAuthUrl(),
                        width: 600,
                        height: 400,
                        successCallback: (token) => {
                            authCache.delegatedToken = token;
                            authCache.isAuthenticated = true;
                            sessionStorage.setItem('delegatedToken', token);
                            resolve(token);
                        },
                        failureCallback: reject
                    });
                });
            } catch (error) {
                console.error('[index.html] Pre-authentication failed:', error);
                authCache.isAuthenticated = false;
                const os = detectOS();
                const isDesktopClient = isTeamsDesktopClient();
                if (os === 'linux' && isDesktopClient) {
                    console.log('[index.html] 🐧 Linux popup blocked - showing browser message');
                    showLinuxMessage();
                }
            }
        }

        // Check if token is still valid
        function isTokenValid() {
            return authCache.isAuthenticated &&
                   authCache.delegatedToken &&
                   authCache.tokenExpiry &&
                   Date.now() < authCache.tokenExpiry;
        }

        // Get valid token (refresh if needed)
        async function getValidToken() {
            // Use cached token if valid
            if (authCache.isAuthenticated && authCache.delegatedToken) {
                try {
                    const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                        headers: { 'Authorization': `Bearer ${authCache.delegatedToken}` }
                    });
                    if (response.ok) {
                        sessionStorage.setItem('delegatedToken', authCache.delegatedToken);
                        return authCache.delegatedToken;
                    }
                } catch (err) {
                    // Ignore and fallback
                }
            }
            // Try session token if available
            const sessionToken = sessionStorage.getItem('delegatedToken');
            if (sessionToken) {
                try {
                    const response = await fetch('https://graph.microsoft.com/v1.0/me', {
                        headers: { 'Authorization': `Bearer ${sessionToken}` }
                    });
                    if (response.ok) {
                        authCache.delegatedToken = sessionToken;
                        authCache.isAuthenticated = true;
                        return sessionToken;
                    }
                } catch (err) {
                    // Ignore and fallback
                }
            }
            // If no valid token, authenticate and store
            return await new Promise((resolve, reject) => {
                microsoftTeams.authentication.authenticate({
                    url: window.APP_CONFIG.getAuthUrl(),
                    width: 600,
                    height: 400,
                    successCallback: (token) => {
                        authCache.delegatedToken = token;
                        authCache.isAuthenticated = true;
                        sessionStorage.setItem('delegatedToken', token);
                        resolve(token);
                    },
                    failureCallback: reject
                });
            });
        }

        if (typeof microsoftTeams !== 'undefined') {
            try {
                microsoftTeams.initialize(async () => {
                    microsoftTeams.appInitialization.notifyAppLoaded();
                    microsoftTeams.appInitialization.notifySuccess();

                    // Start pre-authentication immediately
                    await preAuthenticate();

                    // Request microphone permission on app launch
                    requestMicrophonePermission();
                     // CRITICAL: Start pre-fetching operations immediately during recording
                    preFetchDuringAppLoad().catch(err => {
                        console.warn('[index.html] Pre-fetch failed, will retry during send:', err);
                    });
                });
            } catch (error) {
                console.error('[index.html] Teams SDK initialization failed:', error);
                showStatusMessage('Failed to initialize Teams app. Please refresh.', false);
            }
        } else {
            console.error('[index.html] Microsoft Teams SDK not loaded');
            showStatusMessage('Teams SDK failed to load. Please refresh.', false);
        }

        async function requestMicrophonePermission() {
            let permissionResolved = false;
            let permissionTimeout;
            try {
                // Start a 1 second timer to show warning if not resolved
                const permissionPromise = navigator.mediaDevices.getUserMedia({ audio: true })
                    .then((mediaStream) => {
                        permissionResolved = true;
                        clearTimeout(permissionTimeout);
                        stream = mediaStream;
                        return true;
                    })
                    .catch((error) => {
                        permissionResolved = true;
                        clearTimeout(permissionTimeout);
                        console.error('[index.html] Microphone permission error:', error);
                        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                            logAndShow('Please enable microphone access: Teams Settings → Apps → Citrus Voice Message → Permissions → Allow microphone');
                        } else if (error.name === 'NotFoundError') {
                            logAndShow('No microphone found. Please connect a microphone and try again.');
                        } else {
                            logAndShow('Microphone access failed. Please check your device settings.');
                        }
                        return false;
                    });
                // Set timeout for 1 seconds
                permissionTimeout = setTimeout(() => {
                    if (!permissionResolved) {
                        logAndShow('Please enable microphone access: Teams Settings → Apps → Citrus Voice Message → Permissions → Allow microphone');
                    }
                }, 1000);
                return await permissionPromise;
            } catch (error) {
                clearTimeout(permissionTimeout);
                console.error('[index.html] Microphone permission error:', error);
                logAndShow('Microphone access failed. Please check your device settings.');
                return false;
            }
        }

        const recordButton = document.getElementById('recordButton');
        const recordingArea = document.getElementById('recordingArea');
        const previewSection = document.getElementById('previewSection');

        if (recordButton) {
            recordButton.addEventListener('click', async () => {
                // Check for Linux in Teams desktop client before allowing recording
                const os = detectOS();
                const isDesktopClient = isTeamsDesktopClient();

                if (os === 'linux' && isDesktopClient) {
                    console.log('[index.html] 🐧 Ubuntu teams-for-linux detected - recording blocked due to popup restrictions');
                    logAndShow('⚠️ Please use Teams in your web browser on Ubuntu/Linux', false);
                    return;
                }

                if (mediaRecorder && mediaRecorder.state === 'recording') {
                    // Stop recording
                    mediaRecorder.stop();
                    clearInterval(recordingTimer);

                    // Update UI
                    recordButton.classList.remove('recording');
                    recordButton.innerHTML = '<i class="fas fa-microphone record-icon"></i>';
                    recordingArea.classList.remove('recording');
                    document.getElementById('statusMessage').classList.remove('recording');
                    document.getElementById('statusMessage').textContent = 'Recording complete';
                    document.getElementById('recordingTimer').textContent = '';

                    // Show preview section
                    previewSection.classList.add('show');
                } else {
                    // Start recording
                    if (!stream) {
                        const permissionGranted = await requestMicrophonePermission();
                        if (!permissionGranted) return;
                    }

                    // Ultra-low bitrate for voice messages - optimized for speed and size
                    mediaRecorder = new MediaRecorder(stream, {
                        mimeType: 'audio/webm;codecs=opus',
                        audioBitsPerSecond: 8000  // Ultra-low bitrate for voice (8kbps)
                    });
                    audioChunks = [];
                    recordingDuration = 0;

                    mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            audioChunks.push(event.data);
                        }
                    };

                    mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                        const preview = document.getElementById('preview');
                        preview.src = URL.createObjectURL(audioBlob);
                    };

                    // Start recording
                    mediaRecorder.start(1000);

                    // Update UI
                    recordButton.classList.add('recording');
                    recordButton.innerHTML = '<i class="fas fa-stop record-icon"></i>';
                    recordingArea.classList.add('recording');
                    document.getElementById('statusMessage').classList.add('recording');
                    previewSection.classList.remove('show');

                    // Start timer
                    recordingTimer = setInterval(() => {
                        recordingDuration++;
                        updateTimerDisplay();
                        if (recordingDuration >= 300) {
                            mediaRecorder.stop();
                            clearInterval(recordingTimer);
                            logAndShow('Recording stopped (max duration reached)');
                        }
                    }, 1000);
                    await preAuthenticate();

                }
            });
        }

        function updateTimerDisplay() {
            const minutes = Math.floor(recordingDuration / 60);
            const seconds = recordingDuration % 60;
            const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('recordingTimer').textContent = timeString;
        }

        document.getElementById('sendButton').addEventListener('click', async () => {
            try {
                if (audioChunks.length === 0) {
                    logAndShow('Please record a message first', true);
                    return;
                }
                // Initialize socket connection when sending (only now, not during recording)
                logAndShow('⚠️ Please keep this window open – Sending audio.');
                try {
                    socket = await initializeSocket();
                } catch (error) {
                    logAndShow('❌ Cannot connect to server. Please try again.', false);
                    return;
                }

                const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });

                // ULTRA-FAST: Use cached authentication (no delay!)
                let delegatedToken;
                try {
                    delegatedToken = await getValidToken();
                    console.log('[index.html] ⚡ Using cached authentication - ZERO delay!');
                } catch (error) {
                    console.error('[index.html] Cached token failed, falling back to fresh auth:', error.message);
                    logAndShow(`Authentication required: ${error.message}`, true);
                    throw error;
                }

                if (!delegatedToken) {
                    throw new Error('No token available');
                }
                // Show clear warning to user not to close the app
                logAndShow('⚠️ Please keep this window open – Sending audio.');

                // Show prominent warning banner
                const processingWarning = document.getElementById('processingWarning');
                processingWarning.classList.add('show');

                document.getElementById('sendButton').disabled = true;
                document.getElementById('discardButton').disabled = true;

                // Start reading the file
                const reader = new FileReader();
                reader.readAsDataURL(audioBlob);
                reader.onloadend = async () => {
                    const base64Audio = reader.result.split(',')[1];

                    // ULTRA-FAST: Use cached context (no API call delay!)
                    const context = authCache.userContext || preFetchedData.userContext;
                    if (!context) {
                        throw new Error('No user context available - please refresh the app');
                    }
                    console.log('[index.html] ⚡ Using cached user context - ZERO delay!');

                    const payload = {
                        audioBlob: base64Audio,
                        senderId: context.userObjectId || context.userId,
                        recipientId: null,
                        chatType: context.chatId ? 'oneOnOne' : 'channel',
                        teamId: context.teamId,
                        channelId: context.chatId || context.channelId,
                        delegatedToken: delegatedToken,
                        // Include pre-fetched data to skip server-side operations
                        preFetchedData: {
                            chatMembers: preFetchedData.chatMembers,
                            folderReady: preFetchedData.folderReady,
                            botAdded: preFetchedData.botAdded
                        }
                    };
                    socket.emit('upload-voice-message', payload);
                    // Close app immediately to avoid blocking user (processing continues in background)
                    // setTimeout(() => {
                    //     try {
                    //         if (typeof microsoftTeams !== 'undefined') {
                    //             microsoftTeams.tasks.submitTask({
                    //                 type: 'result',
                    //                 value: {
                    //                     success: true,
                    //                     message: 'Voice message sent successfully! Processing in background.',
                    //                     timestamp: new Date().toISOString()
                    //                 }
                    //             });
                    //         } else {
                    //             window.close();
                    //         }
                    //     } catch (error) {
                    //         console.log('[index.html] Could not close via Teams API, trying window.close()');
                    //         window.close();
                    //     }
                    // }, 1500); // 1.5 second delay to show success message
                };
            } catch (error) {
                console.error('[index.html] Error processing voice message:', error);
                logAndShow(`❌ Failed to send message: ${error.message}`, true);

                // Hide warning banner and re-enable buttons
                const processingWarning = document.getElementById('processingWarning');
                processingWarning.classList.remove('show');

                document.getElementById('sendButton').disabled = false;
                document.getElementById('discardButton').disabled = false;

                if (socket?.connected) {
                    socket.disconnect();
                }
            }
        });

        document.getElementById('discardButton').addEventListener('click', () => {
            audioChunks = [];
            previewSection.classList.remove('show');
            document.getElementById('statusMessage').classList.remove('recording');
            document.getElementById('statusMessage').textContent = 'Tap to start recording';
            document.getElementById('recordingTimer').textContent = '';
            logAndShow('Recording discarded');
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
            }
        });

        socket?.on('upload-success', (data) => {
            logAndShow('✅ Voice message sent successfully!');

            // Hide warning banner
            const processingWarning = document.getElementById('processingWarning');
            processingWarning.classList.remove('show');

            // Close the app IMMEDIATELY upon successful send
            console.log('[index.html] Closing app after successful send...');

            try {
                if (typeof microsoftTeams !== 'undefined') {
                    microsoftTeams.tasks.submitTask({
                        type: 'result',
                        value: {
                            success: true,
                            message: 'Voice message sent successfully!',
                            fileName: data.fileName,
                            downloadUrl: data.downloadUrl
                        }
                    });
                } else {
                    window.close();
                }
            } catch (error) {
                console.log('[index.html] Could not close via Teams API, trying window.close()');
                window.close();
            }
        });

        socket?.on('upload-error', (error) => {
            console.error('[index.html] Upload error:', error);
            logAndShow(`❌ Failed to send message: ${error.details}`, true);

            // Hide warning banner and re-enable buttons on error
            const processingWarning = document.getElementById('processingWarning');
            processingWarning.classList.remove('show');

            document.getElementById('sendButton').disabled = false;
            document.getElementById('discardButton').disabled = false;

            if (socket?.connected) {
                socket.disconnect();
            }
        });



        // PRE-FETCH OPERATIONS DURING APP LOAD (IMMEDIATE)
        async function preFetchDuringAppLoad() {
            try {
                console.log('[index.html] 🚀 Starting immediate pre-fetch operations on app load...');

                const context = authCache.userContext;
                if (!context) {
                    console.warn('[index.html] No user context available for pre-fetch');
                    return;
                }

                // Get the delegated token for API authentication
                const delegatedToken = authCache.delegatedToken;
                if (!delegatedToken) {
                    console.warn('[index.html] No delegated token available for API calls');
                    return;
                }

                // Create authenticated headers
                const authHeaders = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${delegatedToken}`
                };

                // Start all independent operations in parallel immediately with authentication
                const accessTokenPromise = fetch('/api/get-access-token', {
                    method: 'POST',
                    headers: authHeaders,
                    body: JSON.stringify({
                        chatId: context.chatId || context.channelId,
                        userObjectId: context.userObjectId || context.userId
                    })
                }).then(async response => {
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`API Error: ${errorData.message || response.statusText}`);
                    }
                    return response.json();
                });

                const chatMembersPromise = fetch('/api/get-chat-members', {
                    method: 'POST',
                    headers: authHeaders,
                    body: JSON.stringify({
                        chatId: context.chatId || context.channelId
                    })
                }).then(async response => {
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`API Error: ${errorData.message || response.statusText}`);
                    }
                    return response.json();
                });

                const folderPromise = fetch('/api/ensure-teams-chat-files-folder', {
                    method: 'POST',
                    headers: authHeaders,
                    body: JSON.stringify({
                        userObjectId: context.userObjectId || context.userId
                    })
                }).then(async response => {
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`API Error: ${errorData.message || response.statusText}`);
                    }
                    return response.json();
                });

                // Wait for all operations to complete
                const [accessTokenResult, chatMembersResult, folderResult, botResult] = await Promise.all([
                    accessTokenPromise.catch(err => ({ error: err.message })),
                    chatMembersPromise.catch(err => ({ error: err.message })),
                    folderPromise.catch(err => ({ error: err.message })),
                ]);

                // Store results
                if (!accessTokenResult.error) {
                    preFetchedData.accessToken = accessTokenResult.accessToken;
                    console.log('[index.html] ✅ Access token pre-fetched on app load');
                }

                if (!chatMembersResult.error) {
                    preFetchedData.chatMembers = chatMembersResult.members;
                    console.log(`[index.html] ✅ Chat members pre-fetched on app load: ${chatMembersResult.members?.length || 0}`);
                }

                if (!folderResult.error) {
                    preFetchedData.folderReady = true;
                    console.log('[index.html] ✅ Teams Chat Files folder pre-created on app load');
                }

                if (!botResult.error) {
                    preFetchedData.botAdded = true;
                    console.log('[index.html] ✅ Bot pre-added to chat on app load');
                }

                console.log('[index.html] 🎯 ALL PRE-FETCH OPERATIONS COMPLETED ON APP LOAD - READY FOR INSTANT SENDING!');

            } catch (error) {
                console.warn('[index.html] App load pre-fetch error:', error);
            }
        }



        console.log('[index.html] Script initialization complete - ultra-fast voice messaging ready!');
    </script>
</body>
</html>